package config

import (
	"os"

	"gopkg.in/yaml.v3"
)

type Config struct {
	Server ServerConfig `yaml:"server"`
	Chain  ChainConfig  `yaml:"chain"`
	Lark   LarkConfig   `yaml:"lark"`
	Db     DbConfig     `yaml:"db"`
}

type DbConfig struct {
	Host     string `yaml:"host"`
	Port     int    `yaml:"port"`
	User     string `yaml:"user"`
	Password string `yaml:"password"`
	Dbname   string `yaml:"dbname"`
}

type ServerConfig struct {
	Health HealthConfig `yaml:"health"`
}

type HealthConfig struct {
	Port int `yaml:"port"`
}

type ChainConfig struct {
	PolygonRpcUrl string `yaml:"polygon_rpc_url"`
	JuRpcUrl      string `yaml:"ju_rpc_url"`
	MaxBlockStep  int    `yaml:"max_block_step"`
	Seed          string `yaml:"seed"`
}

type LarkConfig struct {
	AppId      string `yaml:"app_id"`
	AppSecret  string `yaml:"app_secret"`
	AppToken   string `yaml:"app_token"`
	Webhook    string `yaml:"webhook"`
	BatchCount int    `yaml:"batch_count"`
}

func LoadConfig(env, confFilePath string) Config {
	configBytes, err := os.ReadFile(confFilePath)
	if err != nil {
		panic(err)
	}

	cfg := Config{}
	err = yaml.Unmarshal(configBytes, &cfg)
	if err != nil {
		panic(err)
	}

	return cfg
}

type HeightConfig struct {
	Height uint64 `yaml:"height"`
}

func LoadHeightConfig(confFilePath string) HeightConfig {
	configBytes, err := os.ReadFile(confFilePath)
	if err != nil {
		panic(err)
	}

	cfg := HeightConfig{}
	err = yaml.Unmarshal(configBytes, &cfg)
	if err != nil {
		panic(err)
	}

	return cfg
}
