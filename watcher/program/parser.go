package program

import (
	"crypto/ecdsa"
	"github.com/ethereum/go-ethereum/core/types"
	"github.com/ethereum/go-ethereum/crypto"
	"github.com/ethereum/go-ethereum/ethclient"
	"github.com/shopspring/decimal"
	common2 "gitlab.jcwork.net/assets-management/sipanzi/common"
	"gitlab.jcwork.net/assets-management/sipanzi/config"
	"gitlab.jcwork.net/assets-management/sipanzi/consts"
	"gitlab.jcwork.net/assets-management/sipanzi/contracts/program"
	"gitlab.jcwork.net/assets-management/sipanzi/lark"
	"gitlab.jcwork.net/assets-management/sipanzi/log"
	"go.uber.org/zap"
)

type Parser struct {
	polygonRpcUrl string
	polyCli       *ethclient.Client

	juRpcUrl string
	juCli    *ethclient.Client

	lark *lark.Lark

	pk *ecdsa.PrivateKey
}

func NewParser(cfg config.Config) Parser {

	polyCli, err := ethclient.Dial(cfg.Chain.PolygonRpcUrl)
	if err != nil {
		panic(err)
	}

	juCli, err := ethclient.Dial(cfg.Chain.JuRpcUrl)
	if err != nil {
		panic(err)
	}

	pk := common2.GetPK(cfg.Chain.Seed)

	log.Logger.Info("get address", zap.String("address", crypto.PubkeyToAddress(pk.PublicKey).String()))

	return Parser{
		polygonRpcUrl: cfg.Chain.PolygonRpcUrl,
		polyCli:       polyCli,
		juRpcUrl:      cfg.Chain.JuRpcUrl,
		juCli:         juCli,
		pk:            pk,
	}
}

func (p Parser) ParseRewardWithdraw(eventLog types.Log) (*common2.Record, error) {
	defer common2.Recover()
	pgm, _ := program.NewProgram(eventLog.Address, p.polyCli)
	rewardWithdrawLog, err := pgm.ParseRewardWithdraw(eventLog)
	if err != nil {
		log.Logger.Error("parse rewardWithdrawLog error", zap.Error(err))
		return nil, err
	}

	log.Logger.Info("parse rewardWithdrawLog", zap.String("from", rewardWithdrawLog.From.String()), zap.String("toJUAmount", decimal.NewFromBigInt(rewardWithdrawLog.ToJUAmount, 0-18).String()), zap.String("txHash", rewardWithdrawLog.Raw.TxHash.String()))

	return &common2.Record{
		Addition: consts.ADDITION,
	}, nil
}
