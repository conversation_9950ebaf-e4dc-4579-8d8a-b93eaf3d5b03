# 导出缺失交易工具使用示例

## 前置条件

1. 确保数据库连接配置正确
2. 确保 Polygon RPC 端点可用
3. 编译导出工具

```bash
go build -o bin/export cmd/export/main.go
```

## 使用场景

### 场景1: 检查最近1000个区块的缺失交易

```bash
# 假设当前最新区块是 50000000，检查最近1000个区块
./bin/export --from 49999000 --output recent_missing.csv
```

### 场景2: 检查特定区块范围的缺失交易

```bash
# 检查区块 45000000 到 45010000 之间的缺失交易
./bin/export --from 45000000 --to 45010000 --output range_missing.csv
```

### 场景3: 使用自定义配置文件

```bash
# 使用生产环境配置
ENV=prd ./bin/export --config config/config-prd.yaml --from 45000000 --output prod_missing.csv
```

## 输出示例

运行成功后，会生成类似以下的CSV文件：

```csv
TxHash,From,Amount,BlockNumber,TxIndex,LogIndex,Timestamp,DateTime
0x1234567890abcdef...,0xabcdef1234567890...,100.5,45000123,5,2,1640995200,2022-01-01 12:00:00
0x9876543210fedcba...,0xfedcba0987654321...,250.75,45000456,12,1,1640995800,2022-01-01 12:10:00
```

## 监控和日志

工具运行时会输出详细的日志信息：

```
2024-01-01T12:00:00Z INFO 开始导出缺失的交易 {"fromBlock": 45000000, "toBlock": 45010000, "outputFile": "missing.csv"}
2024-01-01T12:00:01Z INFO 查询区块范围 {"from": 45000000, "to": 45000999}
2024-01-01T12:00:02Z INFO 找到事件 {"count": 15}
2024-01-01T12:00:03Z INFO 发现缺失交易 {"txHash": "0x1234..."}
2024-01-01T12:00:10Z INFO 导出统计 {"totalRecords": 3, "outputFile": "missing.csv"}
2024-01-01T12:00:10Z INFO 导出完成
```

## 性能考虑

- 工具会自动分批查询（每批1000个区块）
- 每批查询之间有100ms延迟，避免过于频繁的请求
- 大范围查询建议在非高峰时段进行

## 故障排除

### 常见错误

1. **连接数据库失败**
   - 检查配置文件中的数据库连接信息
   - 确保数据库服务正在运行

2. **连接RPC失败**
   - 检查网络连接
   - 验证RPC端点是否可用

3. **权限错误**
   - 确保有写入输出文件的权限
   - 检查输出目录是否存在

### 调试模式

设置环境变量启用详细日志：

```bash
ENV=local ./bin/export --from 45000000 --to 45000010
```

## 数据验证

导出完成后，可以通过以下方式验证数据：

1. 检查CSV文件行数
2. 验证交易哈希格式
3. 确认时间戳范围合理
4. 抽查几个交易在区块链浏览器上的信息
