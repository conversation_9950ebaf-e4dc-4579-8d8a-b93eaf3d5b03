# 导出缺失交易工具

## 功能说明

这个工具用于查询区块链上的 RewardWithdraw 事件，并导出那些在数据库中不存在的交易记录。

## 使用方法

### 编译

```bash
go build -o bin/export cmd/export/main.go
```

### 运行

```bash
./bin/export --from <起始区块号> [选项]
```

### 命令行参数

- `--from <区块号>`: **必需** 起始区块号
- `--to <区块号>`: 可选，结束区块号（默认为最新区块）
- `--config <路径>`: 可选，配置文件路径（默认: config/config.yaml）
- `--output <文件名>`: 可选，输出CSV文件路径（默认: missing_transactions.csv）

### 示例

1. 导出从区块 1000000 到最新区块的缺失交易：
```bash
./bin/export --from 1000000
```

2. 导出指定区块范围的缺失交易：
```bash
./bin/export --from 1000000 --to 1100000
```

3. 指定输出文件：
```bash
./bin/export --from 1000000 --output my_missing_tx.csv
```

## 输出格式

工具会生成一个CSV文件，包含以下字段：

- `TxHash`: 交易哈希
- `From`: 发送方地址
- `Amount`: 金额（JU代币数量）
- `BlockNumber`: 区块号
- `TxIndex`: 交易在区块中的索引
- `LogIndex`: 日志在交易中的索引
- `Timestamp`: 时间戳
- `DateTime`: 可读的日期时间格式

## 工作原理

1. 连接到 Polygon 区块链网络
2. 查询指定区块范围内的 RewardWithdraw 事件
3. 对于每个事件，检查其交易哈希是否已存在于数据库的 `records` 表中
4. 将不存在的交易记录导出到CSV文件

## 注意事项

- 确保配置文件中的数据库连接信息正确
- 确保配置文件中的 Polygon RPC URL 可用
- 大区块范围查询可能需要较长时间，工具会自动分批处理（每批1000个区块）
- 工具会在查询过程中显示进度信息

## 环境变量

可以通过设置 `ENV` 环境变量来指定运行环境：
- `local`: 本地开发环境
- `dev`: 开发环境  
- `test`: 测试环境
- `prd`: 生产环境

如果未设置，默认为 `local`。
