package main

import (
	"context"
	"encoding/csv"
	"flag"
	"fmt"
	common2 "gitlab.jcwork.net/assets-management/sipanzi/common"
	"math/big"
	"os"
	"strconv"
	"time"

	"github.com/ethereum/go-ethereum"
	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/core/types"
	"github.com/ethereum/go-ethereum/ethclient"
	"github.com/shopspring/decimal"
	"gitlab.jcwork.net/assets-management/sipanzi/config"
	"gitlab.jcwork.net/assets-management/sipanzi/consts"
	"gitlab.jcwork.net/assets-management/sipanzi/contracts/program"
	"gitlab.jcwork.net/assets-management/sipanzi/db"
	"gitlab.jcwork.net/assets-management/sipanzi/log"
	"go.uber.org/zap"
)

type EventRecord struct {
	TxHash    string
	From      string
	Amount    string
	BlockNum  uint64
	TxIndex   uint
	LogIndex  uint
	Timestamp int64
}

func main() {
	var (
		configPath = flag.String("config", "config/config.yaml", "配置文件路径")
		fromBlock  = flag.Uint64("from", 0, "起始区块号")
		toBlock    = flag.Uint64("to", 0, "结束区块号 (0表示最新区块)")
		outputFile = flag.String("output", "missing_transactions.csv", "输出文件路径")
	)
	flag.Parse()

	if *fromBlock == 0 {
		fmt.Println("请指定起始区块号 --from")
		flag.Usage()
		os.Exit(1)
	}

	// 初始化日志
	env := os.Getenv("ENV")
	if env == "" {
		env = "local"
	}
	log.InitLogger(env)

	// 初始化配置
	cfg := config.LoadConfig(env, *configPath)

	// 初始化数据库
	db.InitDb(cfg.Db)

	// 初始化区块链客户端
	polyCli, err := ethclient.Dial(cfg.Chain.PolygonRpcUrl)
	if err != nil {
		log.Logger.Fatal("连接Polygon RPC失败", zap.Error(err))
	}
	defer polyCli.Close()

	// 获取最新区块号
	if *toBlock == 0 {
		latestBlock, err := polyCli.BlockNumber(context.Background())
		if err != nil {
			log.Logger.Fatal("获取最新区块号失败", zap.Error(err))
		}
		*toBlock = latestBlock
	}

	log.Logger.Info("开始导出缺失的交易",
		zap.Uint64("fromBlock", *fromBlock),
		zap.Uint64("toBlock", *toBlock),
		zap.String("outputFile", *outputFile))

	// 查询事件并导出缺失的交易
	err = exportMissingTransactions(polyCli, *fromBlock, *toBlock, *outputFile)
	if err != nil {
		log.Logger.Fatal("导出失败", zap.Error(err))
	}

	log.Logger.Info("导出完成")
}

func exportMissingTransactions(client *ethclient.Client, fromBlock, toBlock uint64, outputFile string) error {
	ctx := context.Background()

	// 分批查询事件，避免单次查询区块范围过大
	const batchSize = 1000
	var allMissingRecords []EventRecord

	for start := fromBlock; start <= toBlock; start += batchSize {
		end := start + batchSize - 1
		if end > toBlock {
			end = toBlock
		}

		log.Logger.Info("查询区块范围", zap.Uint64("from", start), zap.Uint64("to", end))

		// 查询RewardWithdraw事件
		logs, err := client.FilterLogs(ctx, ethereum.FilterQuery{
			FromBlock: big.NewInt(int64(start)),
			ToBlock:   big.NewInt(int64(end)),
			Addresses: []common.Address{common.HexToAddress("******************************************")},
			Topics: [][]common.Hash{
				{common.HexToHash(consts.TopicRewardWithdraw)},
			},
		})

		if err != nil {
			return fmt.Errorf("查询事件失败: %w", err)
		}

		log.Logger.Info("找到事件", zap.Int("count", len(logs)))

		// 处理每个事件
		for _, eventLog := range logs {
			record, err := processEvent(eventLog)
			if err != nil {
				log.Logger.Error("处理事件失败", zap.Error(err), zap.String("txHash", eventLog.TxHash.Hex()))
				continue
			}

			// 检查交易是否已存在于数据库中
			exists, err := isTransactionExists(record.TxHash)
			if err != nil {
				log.Logger.Error("检查交易存在性失败", zap.Error(err), zap.String("txHash", record.TxHash))
				continue
			}

			if !exists {
				allMissingRecords = append(allMissingRecords, *record)
				log.Logger.Info("发现缺失交易", zap.String("txHash", record.TxHash))
			}
		}

		// 添加延迟避免请求过于频繁
		time.Sleep(100 * time.Millisecond)
	}

	// 导出到CSV文件
	return exportToCSV(allMissingRecords, outputFile)
}

func processEvent(eventLog types.Log) (*EventRecord, error) {
	// 解析RewardWithdraw事件
	pgm, err := program.NewProgram(common.Address{}, nil)
	if err != nil {
		return nil, fmt.Errorf("创建program合约实例失败: %w", err)
	}

	rewardWithdrawLog, err := pgm.ParseRewardWithdraw(eventLog)
	if err != nil {
		return nil, fmt.Errorf("解析RewardWithdraw事件失败: %w", err)
	}

	// 获取区块信息以获取时间戳
	ts := common2.GetJuChainBlockTime(int64(eventLog.BlockNumber))

	// 转换金额（从wei转换为以太单位）
	amount := decimal.NewFromBigInt(rewardWithdrawLog.ToJUAmount, -18)

	return &EventRecord{
		TxHash:    eventLog.TxHash.Hex(),
		From:      rewardWithdrawLog.From.Hex(),
		Amount:    amount.String(),
		BlockNum:  eventLog.BlockNumber,
		TxIndex:   eventLog.TxIndex,
		LogIndex:  eventLog.Index,
		Timestamp: ts,
	}, nil
}

func isTransactionExists(txHash string) (bool, error) {
	var count int64
	err := db.GetDb().Model(&db.Record{}).Where("pol_hash = ?", txHash).Count(&count).Error
	if err != nil {
		return false, err
	}
	return count > 0, nil
}

func exportToCSV(records []EventRecord, filename string) error {
	file, err := os.Create(filename)
	if err != nil {
		return fmt.Errorf("创建文件失败: %w", err)
	}
	defer file.Close()

	writer := csv.NewWriter(file)
	defer writer.Flush()

	// 写入CSV头部
	headers := []string{
		"TxHash",
		"From",
		"Amount",
		"BlockNumber",
		"TxIndex",
		"LogIndex",
		"Timestamp",
		"DateTime",
	}

	if err := writer.Write(headers); err != nil {
		return fmt.Errorf("写入CSV头部失败: %w", err)
	}

	// 写入数据行
	for _, record := range records {
		dateTime := time.Unix(record.Timestamp, 0).Format("2006-01-02 15:04:05")
		row := []string{
			record.TxHash,
			record.From,
			record.Amount,
			strconv.FormatUint(record.BlockNum, 10),
			strconv.FormatUint(uint64(record.TxIndex), 10),
			strconv.FormatUint(uint64(record.LogIndex), 10),
			strconv.FormatInt(record.Timestamp, 10),
			dateTime,
		}

		if err := writer.Write(row); err != nil {
			return fmt.Errorf("写入CSV数据失败: %w", err)
		}
	}

	log.Logger.Info("导出统计",
		zap.Int("totalRecords", len(records)),
		zap.String("outputFile", filename))

	fmt.Printf("成功导出 %d 条缺失的交易记录到文件: %s\n", len(records), filename)

	return nil
}
