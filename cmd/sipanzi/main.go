package main

import (
	"flag"
	"gitlab.jcwork.net/assets-management/sipanzi/db"
	"gitlab.jcwork.net/assets-management/sipanzi/job"
	"gitlab.jcwork.net/assets-management/sipanzi/watcher/block"
	"os"

	"gitlab.jcwork.net/assets-management/sipanzi/config"
	"gitlab.jcwork.net/assets-management/sipanzi/consts"
	"gitlab.jcwork.net/assets-management/sipanzi/log"
	"gitlab.jcwork.net/assets-management/sipanzi/server/health"
	"go.uber.org/zap"
)

func main() {
	env := os.Getenv("ENV")
	if env == "" {
		env = consts.EnvLocal
		_ = os.Setenv("ENV", env)
	}
	log.InitLogger(env)

	log.Logger.Info("sipanzi started", zap.String("env", env))

	confFlag := flag.String("conf", "config/config.yaml", "configuration file path")
	hconfFlag := flag.String("hconf", "config/height.yaml", "configuration file path")
	flag.Parse()

	cfg := config.LoadConfig(env, *confFlag)
	hCfg := config.LoadHeightConfig(*hconfFlag)

	db.InitDb(cfg.Db)

	job.NewJobManager(cfg.Chain, cfg.Lark).Start()

	block.NewWatcher(cfg, hCfg).Start()

	health.NewServer(cfg.Server.Health).Serve()

	select {}
}
