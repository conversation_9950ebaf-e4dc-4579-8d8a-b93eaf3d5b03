package lark

import (
	"github.com/google/uuid"
	"gitlab.jcwork.net/assets-management/sipanzi/config"
	"gitlab.jcwork.net/assets-management/sipanzi/consts"
	"gitlab.jcwork.net/assets-management/sipanzi/log"
	"testing"
)

func init() {
	log.InitLogger(consts.EnvLocal)
}

func TestLark_CreateApp(t *testing.T) {
	lark := NewLark(config.LarkConfig{
		AppId:     "cli_a8e3c55af7f81028",
		AppSecret: "20TmTtMwch9xxiFhGbOhRhiR3PaSwYcC",
	})

	lark.createBitable("POL-JU")
}

func TestLark_ListTable(t *testing.T) {
	lark := NewLark(config.LarkConfig{
		AppId:     "cli_a8e3c55af7f81028",
		AppSecret: "20TmTtMwch9xxiFhGbOhRhiR3PaSwYcC",
		AppToken:  "GLR5bswYdaAunGsgThClGnxRg3f",
	})

	tableId, err := lark.LastTableId()
	if err != nil {
		t.Fatal(err)

	}

	println(tableId)
}

func TestLark_TableRecordCount(t *testing.T) {
	lark := NewLark(config.LarkConfig{
		AppId:     "cli_a8e3c55af7f81028",
		AppSecret: "20TmTtMwch9xxiFhGbOhRhiR3PaSwYcC",
		AppToken:  "GLR5bswYdaAunGsgThClGnxRg3f",
	})

	count, err := lark.TableRecordCount("tblY4c2hGxGlcwMG")
	if err != nil {
		t.Fatal(err)

	}

	println(count)
}

func TestLark_CreateTable(t *testing.T) {
	lark := NewLark(config.LarkConfig{
		AppId:     "cli_a8e3c55af7f81028",
		AppSecret: "20TmTtMwch9xxiFhGbOhRhiR3PaSwYcC",
		AppToken:  "GLR5bswYdaAunGsgThClGnxRg3f",
	})

	random, _ := uuid.NewRandom()
	lark.CreateTable(random.String()[:8], "交易记录")
}

func TestLark_Update(t *testing.T) {
	lark := NewLark(config.LarkConfig{
		AppId:     "cli_a8e3c55af7f81028",
		AppSecret: "20TmTtMwch9xxiFhGbOhRhiR3PaSwYcC",
		AppToken:  "GLR5bswYdaAunGsgThClGnxRg3f",
	})

	lark.updateApp()
}

func TestLark_ListRole(t *testing.T) {
	lark := NewLark(config.LarkConfig{
		AppId:     "cli_a8e3c55af7f81028",
		AppSecret: "20TmTtMwch9xxiFhGbOhRhiR3PaSwYcC",
		AppToken:  "GLR5bswYdaAunGsgThClGnxRg3f",
	})

	lark.listRole("tblY4c2hGxGlcwMG")
}

func TestLark_ListRoleMember(t *testing.T) {
	lark := NewLark(config.LarkConfig{
		AppId:     "cli_a8e3c55af7f81028",
		AppSecret: "20TmTtMwch9xxiFhGbOhRhiR3PaSwYcC",
		AppToken:  "GLR5bswYdaAunGsgThClGnxRg3f",
	})

	lark.listRoleMember()
}

func TestLark_AddRoleMember(t *testing.T) {
	lark := NewLark(config.LarkConfig{
		AppId:     "cli_a8e3c55af7f81028",
		AppSecret: "20TmTtMwch9xxiFhGbOhRhiR3PaSwYcC",
		AppToken:  "GLR5bswYdaAunGsgThClGnxRg3f",
	})

	lark.addRoleMember()
}
