package common

import (
	"crypto/ecdsa"
	"crypto/md5"
	"crypto/sha256"
	"fmt"
	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/crypto"
	"github.com/shopspring/decimal"
	"gitlab.jcwork.net/assets-management/sipanzi/consts"
	"gitlab.jcwork.net/assets-management/sipanzi/log"
	"go.uber.org/zap"
	"math/big"
	"net/http"
	"os"
	"runtime/debug"
	"time"
)

var DefaultLocation = time.FixedZone("Asia/Shanghai", 8*60*60)

type Metadata struct {
	Name        string `json:"name"`
	Symbol      string `json:"symbol"`
	Description string `json:"description"`
	Image       string `json:"image"`
	CreatedOn   string `json:"createdOn"`
	Twitter     string `json:"twitter"`
	Telegram    string `json:"telegram"`
	Website     string `json:"website"`
}

func GetWithRetry(reqUrl string, times int, d time.Duration) (res *http.Response, err error) {
	for i := 0; i < times; i++ {
		res, err = http.Get(reqUrl)
		if err == nil {
			return res, nil
		}
		time.Sleep(d)
	}
	return nil, err
}

func Recover() {
	if r := recover(); r != nil {
		log.Logger.Error("Recovered", zap.String("panic", fmt.Sprintf("%v", r)), zap.String("stack", string(debug.Stack())))
	}
}

func SqrtPriceToPrice(sqrtPriceX64 *big.Int, decimals0, decimals1 uint8) *big.Float {
	// 1. 计算 2^64
	twoPow64 := new(big.Int).Exp(big.NewInt(2), big.NewInt(64), nil)

	// 2. 将 sqrtPriceX64 转换为 big.Float
	sqrtPriceFloat := new(big.Float).SetInt(sqrtPriceX64)
	sqrtPriceFloat = new(big.Float).Quo(sqrtPriceFloat, new(big.Float).SetInt(twoPow64))

	// 3. 平方得到价格比例
	priceRatio := new(big.Float).Mul(sqrtPriceFloat, sqrtPriceFloat)

	// 4. 处理代币小数位数
	decimalsFactor := new(big.Float).SetInt(
		new(big.Int).Exp(big.NewInt(10), big.NewInt(int64(decimals0-decimals1)), nil),
	)

	// 5. 最终价格计算
	finalPrice := new(big.Float).Mul(priceRatio, decimalsFactor)

	return finalPrice
}

func IsEnvLocal() bool {
	return os.Getenv("ENV") == consts.EnvLocal
}

func GetPK(seed string) *ecdsa.PrivateKey {
	sum := md5.Sum([]byte(seed))
	sum1 := sha256.Sum256(sum[:])
	sum2 := md5.Sum(sum1[:])
	pkBytes := sha256.Sum256(sum2[:])
	pk, err := crypto.HexToECDSA(fmt.Sprintf("%x", pkBytes))
	if err != nil {
		panic(err)
	}

	return pk
}

type Record struct {
	PolHash          common.Hash
	From             common.Address
	PolAmount        decimal.Decimal
	JuAp             float64
	PolAp            float64
	Addition         float64
	Rate             float64
	JuAmount         decimal.Decimal
	TransferJuAmount decimal.Decimal
}

func GetJuChainBlockTime(height int64) int64 {
	return 1742914214 + height
}
