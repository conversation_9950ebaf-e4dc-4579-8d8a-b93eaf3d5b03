#!/bin/bash

# 测试导出工具的脚本

echo "=== 导出缺失交易工具测试 ==="

# 检查二进制文件是否存在
if [ ! -f "bin/export" ]; then
    echo "错误: bin/export 不存在，请先编译"
    echo "运行: go build -o bin/export cmd/export/main.go"
    exit 1
fi

# 检查配置文件是否存在
if [ ! -f "config/config.yaml" ]; then
    echo "错误: config/config.yaml 不存在"
    exit 1
fi

echo "1. 测试帮助信息..."
./bin/export --help

echo ""
echo "2. 测试缺少必需参数..."
./bin/export

echo ""
echo "3. 测试参数验证完成"
echo ""
echo "要实际运行导出，请使用类似以下命令："
echo "./bin/export --from 1000000 --to 1000010 --output test_output.csv"
echo ""
echo "注意: 实际运行需要："
echo "- 正确的数据库连接配置"
echo "- 可用的 Polygon RPC 端点"
echo "- 网络连接"
